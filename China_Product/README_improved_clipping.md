# 改进的流域裁剪和面积加权降雨量计算系统

## 概述

`3_improved_watershed_clipping.py` 是对原始 `2_batch_clip_grb2.py` 的重大改进，实现了更精确的流域尺度降雨量计算方法。

## 主要改进

### 1. 精确的空间相交处理
- **原方法**: 只保留网格中心点在流域内的网格单元
- **新方法**: 保留所有与流域边界相交的网格单元，包括部分重叠的网格

### 2. 面积加权降雨量计算
- **原方法**: 直接输出所有网格点的降雨数据
- **新方法**: 计算每个网格与流域的相交面积，使用面积加权方法计算流域平均降雨量

### 3. 输出格式优化
- **原方法**: 输出包含所有网格点的详细数据
- **新方法**: 每个流域输出单一的加权平均降雨值

### 4. 文件组织改进
- **原方法**: 所有文件保存在同一目录
- **新方法**: 为每个日期创建单独的文件夹，便于管理

## 技术特点

### 空间计算方法
- 使用 Shapely 几何库进行精确的空间相交计算
- 采用中国Albers等积圆锥投影进行面积计算，确保精度
- 支持复杂的流域边界几何形状

### 面积加权公式
```
流域降雨量 = Σ(网格降雨值 × 相交面积 / 流域总面积)
```

其中：
- 完全在流域内的网格：使用整个网格面积
- 部分相交的网格：只使用相交部分的面积

### 网格面积计算
使用球面几何方法计算网格单元面积：
```python
# 纬度方向距离（恒定）
lat_dist = 地球半径 × 网格大小（弧度）

# 经度方向距离（随纬度变化）
lon_dist = 地球半径 × 网格大小（弧度） × cos(纬度)

# 网格面积
area = lat_dist × lon_dist
```

## 使用方法

### 1. 基本使用
```bash
python 3_improved_watershed_clipping.py
```

### 2. 测试功能
```bash
python test_improved_clipping.py
```

### 3. 自定义参数
```python
from improved_watershed_clipping import ImprovedWatershedClipper

# 创建裁剪器实例
clipper = ImprovedWatershedClipper(grid_resolution=0.25)

# 处理单个文件
result, df = clipper.clip_grib_with_improved_method(
    grib_file="path/to/file.GRB2",
    shapefile_path="path/to/watershed.shp",
    output_csv="output.csv"
)
```

## 输出格式

### 文件结构
```
test/
├── 20240328/
│   ├── lianghe.csv
│   ├── yantang.csv
│   └── yinhe.csv
├── 20240329/
│   ├── lianghe.csv
│   ├── yantang.csv
│   └── yinhe.csv
└── ...
```

### CSV文件格式
每个流域的CSV文件包含两列：
- `valid_time`: 预报有效时间
- `tp`: 面积加权的流域平均降雨量

示例：
```csv
valid_time,tp
2024-03-28 08:00:00,0.002456
```

## 配置参数

### 流域边界文件
```python
watersheds = {
    'lianghe': '/path/to/boundary/lianghe.shp',
    'yantang': '/path/to/boundary/yantang.shp',
    'yinhe': '/path/to/boundary/yinhe.shp'
}
```

### 目录设置
```python
source_directory = "/path/to/China_Product/2025_final"  # GRB2文件源目录
output_directory = "/path/to/China_Product/test"        # 输出目录
```

## 依赖库

确保安装以下Python库：
```bash
pip install xarray geopandas rioxarray shapely pandas numpy pyproj
```

## 性能特点

### 内存管理
- 自动释放大型数据集的内存
- 使用垃圾回收机制防止内存泄漏
- 适合处理大量文件的批量操作

### 处理速度
- 针对中国区域优化的投影参数
- 高效的空间相交算法
- 并行处理友好的设计

## 验证和测试

### 测试流程
1. 运行 `test_improved_clipping.py` 验证单文件处理
2. 检查输出的面积加权降雨量是否合理
3. 对比新旧方法的结果差异
4. 运行完整批量处理

### 质量检查
- 验证流域总面积计算的准确性
- 检查网格相交面积的合理性
- 确认加权降雨量的数值范围

## 故障排除

### 常见问题
1. **坐标系不匹配**: 确保所有shapefile使用WGS84坐标系
2. **内存不足**: 减少同时处理的文件数量
3. **投影错误**: 检查pyproj库的安装和版本

### 调试信息
程序会输出详细的处理信息，包括：
- 流域总面积
- 有效网格数量
- 加权降雨量计算结果
- 错误和警告信息

## 与原方法的对比

| 特性 | 原方法 | 改进方法 |
|------|--------|----------|
| 空间处理 | 简单裁剪 | 精确相交 |
| 面积计算 | 无 | 面积加权 |
| 输出格式 | 多点数据 | 单一值 |
| 精度 | 中等 | 高 |
| 适用性 | 数据分析 | 水文建模 |

改进方法更适合作为水文模型的输入数据，提供了更准确的流域尺度降雨估算。
