#!/usr/bin/env python3
"""
测试改进的流域裁剪功能
用于验证新的面积加权降雨量计算方法
"""

import sys
from pathlib import Path
import pandas as pd

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# 直接导入模块文件
import importlib.util
spec = importlib.util.spec_from_file_location("improved_watershed_clipping",
                                              current_dir / "3_improved_watershed_clipping.py")
improved_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(improved_module)

ImprovedWatershedClipper = improved_module.ImprovedWatershedClipper

def test_single_file():
    """测试单个文件的处理"""
    
    print("=" * 60)
    print("🧪 测试改进的流域裁剪功能")
    print("=" * 60)
    
    # 定义测试参数
    test_grib_file = "/home/<USER>/Flood_flow_prediction/China_Product/2025_final"
    test_shapefile = "/home/<USER>/Flood_flow_prediction/boundary/yantang.shp"
    output_csv = "/home/<USER>/Flood_flow_prediction/China_Product/test_output.csv"
    
    # 查找第一个可用的GRB2文件进行测试
    source_path = Path(test_grib_file)
    if not source_path.exists():
        print(f"❌ 测试目录不存在: {test_grib_file}")
        return False
    
    # 查找第一个包含GRB2文件的日期文件夹
    grb2_file = None
    for date_folder in sorted(source_path.iterdir()):
        if date_folder.is_dir() and date_folder.name.isdigit():
            grb2_files = list(date_folder.glob("*.GRB2"))
            if grb2_files:
                grb2_file = grb2_files[0]
                print(f"📁 找到测试文件: {grb2_file}")
                break
    
    if not grb2_file:
        print("❌ 未找到可用的GRB2测试文件")
        return False
    
    # 检查shapefile是否存在
    if not Path(test_shapefile).exists():
        print(f"❌ 流域边界文件不存在: {test_shapefile}")
        return False
    
    print(f"🗺️  使用流域边界: {test_shapefile}")
    print(f"📄 输出文件: {output_csv}")
    
    # 创建裁剪器实例
    clipper = ImprovedWatershedClipper()
    
    try:
        # 执行测试
        print(f"\n🔄 开始处理...")
        processing_result, result_df = clipper.clip_grib_with_improved_method(
            str(grb2_file),
            test_shapefile,
            output_csv
        )
        
        if processing_result['success']:
            print(f"\n✅ 测试成功！")
            print(f"📊 处理结果:")
            print(f"   流域总面积: {processing_result['watershed_area']:.2f} 平方公里")
            print(f"   总网格数: {processing_result['total_grids']}")
            print(f"   有效网格数: {processing_result['valid_grids']}")
            print(f"   有效网格比例: {processing_result['valid_grids']/processing_result['total_grids']*100:.1f}%")
            print(f"   加权降雨量: {processing_result['weighted_rainfall']:.6f}")
            
            # 显示结果DataFrame
            if result_df is not None:
                print(f"\n📋 输出数据:")
                print(result_df)
                
                # 检查输出文件
                if Path(output_csv).exists():
                    saved_df = pd.read_csv(output_csv)
                    print(f"\n💾 已保存到文件:")
                    print(saved_df)
                else:
                    print(f"⚠️  输出文件未找到: {output_csv}")
            
            # 显示前几个网格贡献的详细信息
            if 'grid_contributions' in processing_result:
                contributions = processing_result['grid_contributions']
                print(f"\n🔍 前5个网格单元的贡献详情:")
                for i, contrib in enumerate(contributions[:5]):
                    print(f"   网格 {i+1}:")
                    print(f"     位置: ({contrib['latitude']:.4f}°N, {contrib['longitude']:.4f}°E)")
                    print(f"     降雨值: {contrib['rainfall_value']:.6f}")
                    print(f"     相交面积: {contrib['intersection_area']:.4f} 平方公里")
                    print(f"     面积权重: {contrib['area_weight']:.6f}")
                    print(f"     加权贡献: {contrib['weighted_contribution']:.6f}")
            
            return True
        else:
            print(f"❌ 测试失败: {processing_result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def compare_methods():
    """比较新旧方法的差异"""
    print(f"\n" + "=" * 60)
    print("🔄 方法对比说明")
    print("=" * 60)
    print("原始方法 (2_batch_clip_grb2.py):")
    print("  ✓ 使用 rio.clip() 进行简单裁剪")
    print("  ✓ 只保留中心点在流域内的网格")
    print("  ✓ 直接输出所有网格点的数据")
    print("  ✗ 没有考虑网格与流域的部分重叠")
    print("  ✗ 没有进行面积加权计算")
    
    print(f"\n改进方法 (3_improved_watershed_clipping.py):")
    print("  ✓ 保留所有与流域相交的网格单元")
    print("  ✓ 精确计算网格与流域的相交面积")
    print("  ✓ 使用面积加权方法计算流域平均降雨")
    print("  ✓ 输出单一的流域降雨值")
    print("  ✓ 更准确的流域尺度降雨估算")
    
    print(f"\n📈 预期改进效果:")
    print("  • 更准确的流域边界处理")
    print("  • 更合理的降雨量空间聚合")
    print("  • 更适合水文模型输入的数据格式")

if __name__ == "__main__":
    print("🌧️  改进的流域裁剪功能测试")
    
    # 运行单文件测试
    success = test_single_file()
    
    # 显示方法对比
    compare_methods()
    
    if success:
        print(f"\n🎉 测试完成！新方法工作正常。")
        print(f"💡 建议：如果测试结果满意，可以运行完整的批量处理。")
    else:
        print(f"\n❌ 测试失败，请检查配置和依赖。")
    
    print(f"\n📝 下一步:")
    print(f"   1. 检查测试输出文件的内容")
    print(f"   2. 对比新旧方法的结果差异")
    print(f"   3. 运行完整的批量处理: python 3_improved_watershed_clipping.py")
