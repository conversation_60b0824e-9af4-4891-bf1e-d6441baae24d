#!/usr/bin/env python3
"""
改进的流域裁剪功能 - 实现精确的空间相交和面积加权降雨量计算
基于原始的2_batch_clip_grb2.py，增强了以下功能：
1. 保留所有与流域边界相交的网格单元（不仅仅是中心点在内的）
2. 计算网格单元与流域的相交面积
3. 使用面积加权方法计算流域降雨量
4. 为每个日期创建单独的输出文件夹
"""

import xarray as xr
import geopandas as gpd
from shapely.geometry import box
from shapely.ops import transform
import pandas as pd
from pathlib import Path
import traceback
import gc
import warnings
from pyproj import Transformer
import math

# 忽略警告信息
warnings.filterwarnings('ignore')

class ImprovedWatershedClipper:
    """改进的流域裁剪器类"""
    
    def __init__(self, grid_resolution=0.25):
        """
        初始化裁剪器
        
        Parameters:
        -----------
        grid_resolution : float
            网格分辨率（度），默认0.25度
        """
        self.grid_resolution = grid_resolution
        self.earth_radius = 6371000  # 地球半径（米）
        
    def calculate_grid_cell_area(self, center_lat, center_lon=None, grid_size=None):
        """
        计算网格单元的面积（平方公里）
        使用球面几何计算方法
        
        Parameters:
        -----------
        center_lat : float
            网格中心纬度
        center_lon : float
            网格中心经度
        grid_size : float, optional
            网格大小（度），默认使用类初始化时的分辨率
            
        Returns:
        --------
        float : 网格面积（平方公里）
        """
        if grid_size is None:
            grid_size = self.grid_resolution
            
        # 转换为弧度
        lat_rad = math.radians(center_lat)
        grid_rad = math.radians(grid_size)
        
        # 纬度方向距离（恒定）
        lat_dist = self.earth_radius * grid_rad
        
        # 经度方向距离（随纬度变化）
        lon_dist = self.earth_radius * grid_rad * math.cos(lat_rad)
        
        # 面积（平方米转平方公里）
        return (lat_dist * lon_dist) / 1_000_000
    
    def create_grid_polygon(self, center_lat, center_lon, grid_size=None):
        """
        根据网格中心点创建网格多边形
        
        Parameters:
        -----------
        center_lat : float
            网格中心纬度
        center_lon : float
            网格中心经度
        grid_size : float, optional
            网格大小（度）
            
        Returns:
        --------
        Polygon : 网格多边形
        """
        if grid_size is None:
            grid_size = self.grid_resolution
            
        half_size = grid_size / 2
        
        # 创建网格边界
        min_lat = center_lat - half_size
        max_lat = center_lat + half_size
        min_lon = center_lon - half_size
        max_lon = center_lon + half_size
        
        # 创建多边形
        return box(min_lon, min_lat, max_lon, max_lat)
    
    def calculate_intersection_area(self, grid_polygon, watershed_geometry):
        """
        计算网格多边形与流域几何体的相交面积
        
        Parameters:
        -----------
        grid_polygon : Polygon
            网格多边形
        watershed_geometry : GeoDataFrame.geometry
            流域几何体
            
        Returns:
        --------
        float : 相交面积（平方公里）
        """
        try:
            # 转换到适合面积计算的投影坐标系
            # 使用中国Albers等积圆锥投影
            target_crs = "+proj=aea +lat_1=25 +lat_2=47 +lat_0=36 +lon_0=105 +x_0=0 +y_0=0 +datum=WGS84 +units=m +no_defs"
            
            # 创建坐标转换器
            transformer = Transformer.from_crs("EPSG:4326", target_crs, always_xy=True)
            
            # 转换网格多边形
            grid_projected = transform(transformer.transform, grid_polygon)
            
            # 转换流域几何体
            watershed_projected = transform(transformer.transform, watershed_geometry.unary_union)
            
            # 计算相交
            intersection = grid_projected.intersection(watershed_projected)
            
            if intersection.is_empty:
                return 0.0
            
            # 返回相交面积（平方米转平方公里）
            return intersection.area / 1_000_000
            
        except Exception as e:
            print(f"计算相交面积时出错: {e}")
            return 0.0

    def get_watershed_total_area(self, watershed_gdf):
        """
        计算流域总面积
        
        Parameters:
        -----------
        watershed_gdf : GeoDataFrame
            流域地理数据框
            
        Returns:
        --------
        float : 流域总面积（平方公里）
        """
        try:
            # 转换到适合面积计算的投影坐标系
            target_crs = "+proj=aea +lat_1=25 +lat_2=47 +lat_0=36 +lon_0=105 +x_0=0 +y_0=0 +datum=WGS84 +units=m +no_defs"
            
            # 确保坐标系正确
            if watershed_gdf.crs is None:
                watershed_gdf = watershed_gdf.set_crs("EPSG:4326")
            elif watershed_gdf.crs.to_string() != "EPSG:4326":
                watershed_gdf = watershed_gdf.to_crs("EPSG:4326")
            
            # 转换到投影坐标系
            watershed_projected = watershed_gdf.to_crs(target_crs)
            
            # 计算总面积（平方米转平方公里）
            total_area = watershed_projected.geometry.area.sum() / 1_000_000
            
            return total_area
            
        except Exception as e:
            print(f"计算流域总面积时出错: {e}")
            return 0.0

    def clip_grib_with_improved_method(self, grib_file, shapefile_path, output_csv=None):
        """
        使用改进方法裁剪GRIB2文件
        实现精确的空间相交和面积加权降雨量计算
        
        Parameters:
        -----------
        grib_file : str
            GRIB2文件路径
        shapefile_path : str
            shapefile文件路径
        output_csv : str, optional
            输出CSV文件路径
            
        Returns:
        --------
        tuple : (处理结果字典, DataFrame)
        """
        ds = None
        gdf = None
        
        try:
            print(f"正在处理: {Path(grib_file).name}")
            
            # 1. 读取GRIB2文件
            ds = xr.open_dataset(grib_file, engine='cfgrib')
            
            # 2. 设置空间参考系统
            ds = ds.rio.write_crs("EPSG:4326")
            
            # 3. 读取shapefile
            gdf = gpd.read_file(shapefile_path)
            
            # 确保坐标系正确
            if gdf.crs is None:
                gdf = gdf.set_crs("EPSG:4326")
            elif gdf.crs.to_string() != "EPSG:4326":
                gdf = gdf.to_crs("EPSG:4326")
            
            # 4. 获取流域总面积
            watershed_total_area = self.get_watershed_total_area(gdf)
            print(f"流域总面积: {watershed_total_area:.2f} 平方公里")
            
            # 5. 转换为DataFrame进行处理
            df = ds.to_dataframe().reset_index()
            df = df.dropna()
            
            # 重命名列
            if 'unknown' in df.columns:
                df = df.rename(columns={'unknown': 'tp'})
            
            # 6. 计算每个网格单元的贡献
            grid_contributions = []
            total_weighted_rainfall = 0.0
            
            print(f"开始处理 {len(df)} 个网格单元...")
            
            for _, row in df.iterrows():
                lat, lon = row['latitude'], row['longitude']
                rainfall_value = row['tp']
                
                # 创建网格多边形
                grid_polygon = self.create_grid_polygon(lat, lon)
                
                # 计算与流域的相交面积
                intersection_area = self.calculate_intersection_area(grid_polygon, gdf.geometry)
                
                if intersection_area > 0:
                    # 计算面积权重
                    area_weight = intersection_area / watershed_total_area
                    
                    # 计算加权降雨贡献
                    weighted_contribution = rainfall_value * area_weight
                    total_weighted_rainfall += weighted_contribution
                    
                    # 记录贡献信息
                    grid_contributions.append({
                        'latitude': lat,
                        'longitude': lon,
                        'rainfall_value': rainfall_value,
                        'intersection_area': intersection_area,
                        'area_weight': area_weight,
                        'weighted_contribution': weighted_contribution
                    })
            
            print(f"有效网格单元数: {len(grid_contributions)}")
            print(f"流域加权平均降雨量: {total_weighted_rainfall:.6f}")
            
            # 7. 创建输出结果
            if len(grid_contributions) > 0:
                # 获取时间信息
                time_info = df.iloc[0]
                
                result_data = {
                    'valid_time': time_info['valid_time'],
                    'tp': total_weighted_rainfall
                }
                
                result_df = pd.DataFrame([result_data])
                
                # 保存到CSV
                if output_csv:
                    result_df.to_csv(output_csv, index=False)
                    print(f"结果已保存到: {output_csv}")
                
                # 返回处理结果
                processing_result = {
                    'success': True,
                    'watershed_area': watershed_total_area,
                    'valid_grids': len(grid_contributions),
                    'total_grids': len(df),
                    'weighted_rainfall': total_weighted_rainfall,
                    'grid_contributions': grid_contributions
                }
                
                return processing_result, result_df
            
            else:
                print("警告: 没有找到与流域相交的网格单元")
                return {'success': False, 'error': '没有相交的网格单元'}, None
                
        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            print(f"❌ {error_msg}")
            print(f"详细错误: {traceback.format_exc()}")
            return {'success': False, 'error': error_msg}, None
            
        finally:
            # 清理内存
            if ds is not None:
                ds.close()
                del ds
            if gdf is not None:
                del gdf
            gc.collect()


def process_single_date_folder_improved(date_folder_path, watersheds, output_base_dir):
    """
    使用改进方法处理单个日期文件夹中的GRB2文件

    Parameters:
    -----------
    date_folder_path : Path
        日期文件夹路径
    watersheds : dict
        流域名称和shapefile路径的字典
    output_base_dir : str
        输出基础目录路径

    Returns:
    --------
    dict : 处理结果统计
    """
    results = {
        'date': date_folder_path.name,
        'success': 0,
        'failed': 0,
        'errors': [],
        'skipped': 0,
        'watershed_results': {}
    }

    # 创建输出日期文件夹
    output_date_dir = Path(output_base_dir) / date_folder_path.name
    output_date_dir.mkdir(parents=True, exist_ok=True)

    # 查找GRB2文件
    grb2_files = list(date_folder_path.glob("*.GRB2"))

    if not grb2_files:
        results['errors'].append("未找到GRB2文件")
        results['failed'] = 1
        return results

    if len(grb2_files) > 1:
        results['errors'].append(f"找到多个GRB2文件: {[f.name for f in grb2_files]}")
        results['failed'] = 1
        return results

    grb2_file = grb2_files[0]

    # 初始化裁剪器
    clipper = ImprovedWatershedClipper()

    # 为每个流域进行改进的裁剪处理
    for watershed_name, shapefile_path in watersheds.items():
        try:
            # 创建输出文件路径
            output_filename = f"{watershed_name}.csv"
            output_file_path = output_date_dir / output_filename

            # 检查文件是否已存在
            if output_file_path.exists():
                print(f"⏭️  {results['date']}/{watershed_name}: 文件已存在，跳过")
                results['success'] += 1
                results['skipped'] += 1
                continue

            print(f"🔄 处理 {results['date']}/{watershed_name}...")

            # 执行改进的裁剪
            processing_result, _ = clipper.clip_grib_with_improved_method(
                str(grb2_file),
                shapefile_path,
                str(output_file_path)
            )

            if processing_result['success']:
                print(f"✅ {results['date']}/{watershed_name}: 成功处理")
                print(f"   流域面积: {processing_result['watershed_area']:.2f} 平方公里")
                print(f"   有效网格: {processing_result['valid_grids']}/{processing_result['total_grids']}")
                print(f"   加权降雨: {processing_result['weighted_rainfall']:.6f}")

                results['success'] += 1
                results['watershed_results'][watershed_name] = processing_result
            else:
                error_msg = f"{watershed_name}: {processing_result['error']}"
                results['errors'].append(error_msg)
                results['failed'] += 1
                print(f"❌ {results['date']}/{watershed_name}: {error_msg}")

        except Exception as e:
            error_msg = f"{watershed_name}: {str(e)}"
            results['errors'].append(error_msg)
            results['failed'] += 1
            print(f"❌ {results['date']}/{watershed_name}: 处理失败 - {error_msg}")
            print(f"   详细错误: {traceback.format_exc()}")

    return results


def batch_process_grb2_files_improved(source_dir, watersheds, output_dir, max_folders=None):
    """
    使用改进方法批量处理所有日期文件夹中的GRB2文件

    Parameters:
    -----------
    source_dir : str
        源目录路径
    watersheds : dict
        流域名称和shapefile路径的字典
    output_dir : str
        输出目录路径
    max_folders : int, optional
        最大处理文件夹数量（用于测试）

    Returns:
    --------
    dict : 总体处理结果统计
    """
    source_path = Path(source_dir)
    output_path = Path(output_dir)

    if not source_path.exists():
        print(f"错误：源目录 {source_dir} 不存在！")
        return None

    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)

    # 获取所有日期文件夹
    date_folders = [f for f in source_path.iterdir() if f.is_dir() and f.name.isdigit()]
    date_folders.sort()

    if max_folders:
        date_folders = date_folders[:max_folders]

    print(f"🚀 开始改进的批量处理 {len(date_folders)} 个日期文件夹...")
    print(f"源目录: {source_dir}")
    print(f"输出目录: {output_dir}")
    print(f"流域: {list(watersheds.keys())}")
    print("=" * 80)

    # 统计信息
    total_stats = {
        'total_folders': len(date_folders),
        'processed_folders': 0,
        'total_success': 0,
        'total_failed': 0,
        'failed_folders': [],
        'watershed_stats': {name: {'success': 0, 'failed': 0, 'total_rainfall': 0.0}
                           for name in watersheds.keys()}
    }

    # 处理每个日期文件夹
    for i, date_folder in enumerate(date_folders, 1):
        print(f"\n📁 处理 {i}/{len(date_folders)}: {date_folder.name}")
        print("-" * 60)

        try:
            results = process_single_date_folder_improved(date_folder, watersheds, output_dir)

            total_stats['processed_folders'] += 1
            total_stats['total_success'] += results['success']
            total_stats['total_failed'] += results['failed']

            # 更新流域统计
            for watershed_name in watersheds.keys():
                if watershed_name in results['watershed_results']:
                    watershed_result = results['watershed_results'][watershed_name]
                    total_stats['watershed_stats'][watershed_name]['success'] += 1
                    total_stats['watershed_stats'][watershed_name]['total_rainfall'] += watershed_result['weighted_rainfall']
                else:
                    # 检查是否有该流域的错误
                    watershed_errors = [error for error in results['errors'] if error.startswith(watershed_name)]
                    if watershed_errors:
                        total_stats['watershed_stats'][watershed_name]['failed'] += 1

            if results['failed'] > 0:
                total_stats['failed_folders'].append({
                    'date': results['date'],
                    'errors': results['errors']
                })

        except Exception as e:
            print(f"❌ {date_folder.name}: 处理文件夹失败 - {e}")
            total_stats['failed_folders'].append({
                'date': date_folder.name,
                'errors': [f"文件夹处理失败: {str(e)}"]
            })

    # 输出总体统计结果
    print("\n" + "=" * 80)
    print("📊 改进方法批量处理统计结果：")
    print("=" * 80)
    print(f"总文件夹数: {total_stats['total_folders']}")
    print(f"成功处理: {total_stats['processed_folders']}")
    print(f"总成功裁剪: {total_stats['total_success']}")
    print(f"总失败裁剪: {total_stats['total_failed']}")

    if (total_stats['total_success'] + total_stats['total_failed']) > 0:
        success_rate = total_stats['total_success'] / (total_stats['total_success'] + total_stats['total_failed']) * 100
        print(f"成功率: {success_rate:.1f}%")
    else:
        print("成功率: 0%")

    print(f"\n📈 各流域统计：")
    for watershed_name, stats in total_stats['watershed_stats'].items():
        total_watershed = stats['success'] + stats['failed']
        success_rate = stats['success'] / total_watershed * 100 if total_watershed > 0 else 0
        avg_rainfall = stats['total_rainfall'] / stats['success'] if stats['success'] > 0 else 0
        print(f"  {watershed_name}:")
        print(f"    成功: {stats['success']}, 失败: {stats['failed']}, 成功率: {success_rate:.1f}%")
        print(f"    平均降雨量: {avg_rainfall:.6f}")

    if total_stats['failed_folders']:
        print(f"\n❌ 失败的文件夹 ({len(total_stats['failed_folders'])} 个):")
        for failed in total_stats['failed_folders'][:10]:  # 只显示前10个
            print(f"  {failed['date']}: {', '.join(failed['errors'])}")
        if len(total_stats['failed_folders']) > 10:
            print(f"  ... 还有 {len(total_stats['failed_folders'])-10} 个失败文件夹")

    return total_stats


if __name__ == "__main__":
    # 定义流域和对应的shapefile路径
    watersheds = {
        'lianghe': '/home/<USER>/Flood_flow_prediction/boundary/lianghe.shp',
        'yantang': '/home/<USER>/Flood_flow_prediction/boundary/yantang.shp',
        'yinhe': '/home/<USER>/Flood_flow_prediction/boundary/yinhe.shp'
    }

    # 源目录和输出目录
    source_directory = "/home/<USER>/Flood_flow_prediction/China_Product/2025_final"
    output_directory = "/home/<USER>/Flood_flow_prediction/China_Product/test"

    print("=" * 80)
    print("🌧️  改进的流域裁剪和面积加权降雨量计算系统")
    print("=" * 80)
    print("功能特点:")
    print("✅ 保留所有与流域边界相交的网格单元")
    print("✅ 精确计算网格单元与流域的相交面积")
    print("✅ 使用面积加权方法计算流域降雨量")
    print("✅ 为每个日期创建单独的输出文件夹")
    print("✅ 每个流域生成独立的CSV文件（valid_time, tp）")
    print("=" * 80)

    # 检查源目录是否存在
    if not Path(source_directory).exists():
        print(f"❌ 错误：源目录 {source_directory} 不存在！")
        print("请检查路径是否正确。")
        exit(1)

    # 检查流域边界文件是否存在
    missing_files = []
    for watershed_name, shapefile_path in watersheds.items():
        if not Path(shapefile_path).exists():
            missing_files.append(f"{watershed_name}: {shapefile_path}")

    if missing_files:
        print("❌ 错误：以下流域边界文件不存在：")
        for missing_file in missing_files:
            print(f"   {missing_file}")
        exit(1)

    print(f"📂 源目录: {source_directory}")
    print(f"📂 输出目录: {output_directory}")
    print(f"🗺️  流域数量: {len(watersheds)}")
    for name in watersheds.keys():
        print(f"   - {name}")

    # 先测试处理前3个文件夹
    print(f"\n🧪 测试模式：处理前3个文件夹...")
    test_results = batch_process_grb2_files_improved(
        source_directory,
        watersheds,
        output_directory,
        max_folders=3
    )

    if test_results and test_results['total_success'] > 0:
        print(f"\n✅ 测试成功！处理了 {test_results['total_success']} 个流域文件")
        print("是否继续处理所有文件夹？")
        user_input = input("输入 'y' 继续处理所有文件夹，或按任意键退出: ")

        if user_input.lower() == 'y':
            print(f"\n🚀 开始处理所有文件夹...")
            final_results = batch_process_grb2_files_improved(
                source_directory,
                watersheds,
                output_directory
            )

            if final_results:
                print(f"\n🎉 批量处理完成！")
                print(f"📊 最终统计:")
                print(f"   总成功: {final_results['total_success']}")
                print(f"   总失败: {final_results['total_failed']}")
                print(f"   处理文件夹: {final_results['processed_folders']}/{final_results['total_folders']}")

                # 显示各流域的平均降雨量
                print(f"\n🌧️  各流域平均降雨量:")
                for watershed_name, stats in final_results['watershed_stats'].items():
                    if stats['success'] > 0:
                        avg_rainfall = stats['total_rainfall'] / stats['success']
                        print(f"   {watershed_name}: {avg_rainfall:.6f} (基于 {stats['success']} 个文件)")
                    else:
                        print(f"   {watershed_name}: 无有效数据")
            else:
                print("❌ 批量处理失败")
        else:
            print("👋 用户取消，退出程序。")
    else:
        print("❌ 测试失败，请检查配置和依赖。")
        if test_results:
            print("错误信息:")
            for failed_folder in test_results['failed_folders']:
                print(f"  {failed_folder['date']}: {', '.join(failed_folder['errors'])}")

    print(f"\n📁 输出文件保存在: {output_directory}")
    print("每个日期文件夹包含三个流域的CSV文件：lianghe.csv, yantang.csv, yinhe.csv")
    print("每个CSV文件包含两列：valid_time（预报时间）和 tp（面积加权降雨量）")
