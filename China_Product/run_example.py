#!/usr/bin/env python3
"""
改进的流域裁剪功能使用示例
演示如何使用新的面积加权降雨量计算方法
"""

from pathlib import Path
import sys

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# 导入改进的裁剪模块
import importlib.util
spec = importlib.util.spec_from_file_location("improved_watershed_clipping", 
                                              current_dir / "3_improved_watershed_clipping.py")
improved_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(improved_module)

def run_single_watershed_example():
    """运行单个流域的处理示例"""
    
    print("=" * 60)
    print("🌧️  单个流域处理示例")
    print("=" * 60)
    
    # 配置参数
    source_dir = "/home/<USER>/Flood_flow_prediction/China_Product/2025_final"
    output_dir = "/home/<USER>/Flood_flow_prediction/China_Product/test_single"
    
    # 只处理一个流域进行演示
    watersheds = {
        'yantang': '/home/<USER>/Flood_flow_prediction/boundary/yantang.shp'
    }
    
    print(f"源目录: {source_dir}")
    print(f"输出目录: {output_dir}")
    print(f"处理流域: {list(watersheds.keys())}")
    
    # 检查源目录
    if not Path(source_dir).exists():
        print(f"❌ 源目录不存在: {source_dir}")
        return False
    
    # 运行处理（只处理前2个文件夹作为示例）
    try:
        results = improved_module.batch_process_grb2_files_improved(
            source_dir=source_dir,
            watersheds=watersheds,
            output_dir=output_dir,
            max_folders=2  # 只处理前2个文件夹
        )
        
        if results and results['total_success'] > 0:
            print(f"\n✅ 示例处理成功！")
            print(f"📊 处理结果:")
            print(f"   成功处理: {results['total_success']} 个文件")
            print(f"   失败处理: {results['total_failed']} 个文件")
            
            # 显示流域统计
            for watershed_name, stats in results['watershed_stats'].items():
                if stats['success'] > 0:
                    avg_rainfall = stats['total_rainfall'] / stats['success']
                    print(f"   {watershed_name} 平均降雨量: {avg_rainfall:.6f}")
            
            return True
        else:
            print("❌ 示例处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        return False

def run_full_batch_example():
    """运行完整批量处理示例"""
    
    print("\n" + "=" * 60)
    print("🚀 完整批量处理示例")
    print("=" * 60)
    
    # 配置参数
    source_dir = "/home/<USER>/Flood_flow_prediction/China_Product/2025_final"
    output_dir = "/home/<USER>/Flood_flow_prediction/China_Product/test"
    
    # 所有三个流域
    watersheds = {
        'lianghe': '/home/<USER>/Flood_flow_prediction/boundary/lianghe.shp',
        'yantang': '/home/<USER>/Flood_flow_prediction/boundary/yantang.shp',
        'yinhe': '/home/<USER>/Flood_flow_prediction/boundary/yinhe.shp'
    }
    
    print(f"源目录: {source_dir}")
    print(f"输出目录: {output_dir}")
    print(f"处理流域: {list(watersheds.keys())}")
    
    # 询问用户是否继续
    print(f"\n⚠️  这将处理所有日期文件夹中的GRB2文件")
    print(f"可能需要较长时间，确定要继续吗？")
    user_input = input("输入 'yes' 继续，或按任意键取消: ")
    
    if user_input.lower() != 'yes':
        print("👋 用户取消操作")
        return False
    
    try:
        results = improved_module.batch_process_grb2_files_improved(
            source_dir=source_dir,
            watersheds=watersheds,
            output_dir=output_dir
        )
        
        if results:
            print(f"\n🎉 批量处理完成！")
            print(f"📊 最终统计:")
            print(f"   总成功: {results['total_success']}")
            print(f"   总失败: {results['total_failed']}")
            print(f"   处理文件夹: {results['processed_folders']}/{results['total_folders']}")
            
            # 显示各流域统计
            print(f"\n🌧️  各流域统计:")
            for watershed_name, stats in results['watershed_stats'].items():
                total_processed = stats['success'] + stats['failed']
                success_rate = stats['success'] / total_processed * 100 if total_processed > 0 else 0
                avg_rainfall = stats['total_rainfall'] / stats['success'] if stats['success'] > 0 else 0
                
                print(f"   {watershed_name}:")
                print(f"     成功: {stats['success']}, 失败: {stats['failed']}")
                print(f"     成功率: {success_rate:.1f}%")
                print(f"     平均降雨量: {avg_rainfall:.6f}")
            
            return True
        else:
            print("❌ 批量处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def show_output_structure():
    """显示输出文件结构"""
    
    print("\n" + "=" * 60)
    print("📁 输出文件结构说明")
    print("=" * 60)
    
    print("输出目录结构:")
    print("test/")
    print("├── 20240328/")
    print("│   ├── lianghe.csv")
    print("│   ├── yantang.csv")
    print("│   └── yinhe.csv")
    print("├── 20240329/")
    print("│   ├── lianghe.csv")
    print("│   ├── yantang.csv")
    print("│   └── yinhe.csv")
    print("└── ...")
    
    print(f"\n📄 CSV文件格式:")
    print("每个流域的CSV文件包含两列：")
    print("- valid_time: 预报有效时间")
    print("- tp: 面积加权的流域平均降雨量")
    
    print(f"\n示例内容:")
    print("valid_time,tp")
    print("2024-03-28 08:00:00,0.002456")

if __name__ == "__main__":
    print("🌧️  改进的流域裁剪功能使用示例")
    print("=" * 60)
    
    # 显示菜单
    print("请选择运行模式:")
    print("1. 单个流域处理示例（快速测试）")
    print("2. 完整批量处理示例（处理所有数据）")
    print("3. 查看输出文件结构说明")
    print("4. 退出")
    
    while True:
        choice = input(f"\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            success = run_single_watershed_example()
            if success:
                print(f"\n💡 提示: 检查输出目录查看生成的CSV文件")
            break
            
        elif choice == '2':
            success = run_full_batch_example()
            if success:
                print(f"\n💡 提示: 所有结果已保存到输出目录")
            break
            
        elif choice == '3':
            show_output_structure()
            continue
            
        elif choice == '4':
            print("👋 退出程序")
            break
            
        else:
            print("❌ 无效选择，请输入 1-4")
    
    print(f"\n📚 更多信息请参考:")
    print(f"   - README_improved_clipping.md")
    print(f"   - test_improved_clipping.py")
    print(f"   - 3_improved_watershed_clipping.py")
