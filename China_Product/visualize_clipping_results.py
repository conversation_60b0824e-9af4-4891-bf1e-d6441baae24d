#!/usr/bin/env python3
"""
可视化流域边界裁剪结果
展示原始栅格数据、流域边界和裁剪后保留的栅格点
"""

import os
import xarray as xr
import geopandas as gpd
import rioxarray as rxr
from shapely.geometry import mapping
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib.ticker as ticker
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def set_axis_ticks(ax, lon_bounds, lat_bounds, tick_interval=0.05):
    """
    设置坐标轴刻度为0.05°间隔

    Parameters:
    -----------
    ax : matplotlib.axes.Axes
        坐标轴对象
    lon_bounds : tuple
        经度范围 (min, max)
    lat_bounds : tuple
        纬度范围 (min, max)
    tick_interval : float
        刻度间隔，默认0.05°
    """
    # 计算经度刻度
    lon_min = np.floor(lon_bounds[0] / tick_interval) * tick_interval
    lon_max = np.ceil(lon_bounds[1] / tick_interval) * tick_interval
    lon_ticks = np.arange(lon_min, lon_max + tick_interval, tick_interval)

    # 计算纬度刻度
    lat_min = np.floor(lat_bounds[0] / tick_interval) * tick_interval
    lat_max = np.ceil(lat_bounds[1] / tick_interval) * tick_interval
    lat_ticks = np.arange(lat_min, lat_max + tick_interval, tick_interval)

    # 设置刻度
    ax.set_xticks(lon_ticks)
    ax.set_yticks(lat_ticks)

    # 设置刻度标签格式
    ax.xaxis.set_major_formatter(ticker.FormatStrFormatter('%.3f°'))
    ax.yaxis.set_major_formatter(ticker.FormatStrFormatter('%.3f°'))

    # 设置网格
    ax.grid(True, alpha=0.3, linewidth=0.5)
    ax.set_axisbelow(True)

def draw_grid_cells(ax, lons, lats, values, cmap='Blues', alpha=0.7, edgecolor='black', linewidth=0.1):
    """
    绘制栅格单元格

    Parameters:
    -----------
    ax : matplotlib.axes.Axes
        坐标轴对象
    lons : array
        经度数组
    lats : array
        纬度数组
    values : array
        数值数组
    cmap : str
        颜色映射
    alpha : float
        透明度
    edgecolor : str
        边框颜色
    linewidth : float
        边框线宽
    """
    from matplotlib.patches import Rectangle
    from matplotlib.colors import Normalize
    from matplotlib.cm import get_cmap

    # 计算栅格分辨率
    if len(lons) > 1:
        lon_res = abs(lons[1] - lons[0])
    else:
        lon_res = 0.05

    if len(lats) > 1:
        lat_res = abs(lats[1] - lats[0])
    else:
        lat_res = 0.05

    # 创建颜色映射
    norm = Normalize(vmin=np.nanmin(values), vmax=np.nanmax(values))
    colormap = get_cmap(cmap)

    # 绘制每个栅格单元
    for lon, lat, val in zip(lons, lats, values):
        if not np.isnan(val):
            # 计算栅格单元的边界
            left = lon - lon_res/2
            bottom = lat - lat_res/2

            # 创建矩形
            rect = Rectangle((left, bottom), lon_res, lat_res,
                           facecolor=colormap(norm(val)),
                           edgecolor=edgecolor,
                           linewidth=linewidth,
                           alpha=alpha)
            ax.add_patch(rect)

    return norm, colormap

def load_and_prepare_data(grib_file, shapefile_path):
    """
    加载GRIB2文件和shapefile数据
    
    Parameters:
    -----------
    grib_file : str
        GRIB2文件路径
    shapefile_path : str
        shapefile文件路径
        
    Returns:
    --------
    tuple : (原始数据集, 流域边界, 裁剪后数据集)
    """
    # 1. 读取GRIB2文件
    ds = xr.open_dataset(grib_file, engine='cfgrib')
    ds = ds.rio.write_crs("EPSG:4326")
    
    # 2. 读取shapefile
    gdf = gpd.read_file(shapefile_path)
    if gdf.crs is None:
        gdf = gdf.set_crs("EPSG:4326")
    elif gdf.crs.to_string() != "EPSG:4326":
        gdf = gdf.to_crs("EPSG:4326")
    
    # 3. 进行裁剪
    clipped_ds = ds.rio.clip(gdf.geometry.apply(mapping), gdf.crs, drop=False)
    
    return ds, gdf, clipped_ds

def create_visualization(original_ds, watershed_gdf, clipped_ds, watershed_name, save_path=None):
    """
    创建可视化图表

    Parameters:
    -----------
    original_ds : xarray.Dataset
        原始数据集
    watershed_gdf : geopandas.GeoDataFrame
        流域边界
    clipped_ds : xarray.Dataset
        裁剪后数据集
    watershed_name : str
        流域名称
    save_path : str, optional
        保存路径
    """
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'{watershed_name.upper()} Watershed Clipping Visualization', fontsize=16, fontweight='bold')

    # 获取数据变量名（通常是'unknown'或'tp'）
    data_var = list(original_ds.data_vars)[0]

    # 检查数据维度并选择合适的索引方式
    data_dims = original_ds[data_var].dims
    print(f"Data variable '{data_var}' dimensions: {data_dims}")

    # 根据实际维度选择数据
    if 'time' in data_dims and 'step' in data_dims:
        original_data = original_ds[data_var].isel(time=0, step=0)
        clipped_data = clipped_ds[data_var].isel(time=0, step=0)
    elif 'step' in data_dims:
        original_data = original_ds[data_var].isel(step=0)
        clipped_data = clipped_ds[data_var].isel(step=0)
    else:
        original_data = original_ds[data_var]
        clipped_data = clipped_ds[data_var]

    # 获取流域边界范围
    bounds = watershed_gdf.bounds.iloc[0]
    lon_bounds = (bounds['minx'], bounds['maxx'])
    lat_bounds = (bounds['miny'], bounds['maxy'])

    # 1. 原始栅格数据
    ax1 = axes[0, 0]
    original_data.plot(ax=ax1, cmap='Blues', add_colorbar=True)
    watershed_gdf.boundary.plot(ax=ax1, color='red', linewidth=2)
    ax1.set_title('Original Grid Data with Watershed Boundary', fontweight='bold')
    ax1.set_xlabel('Longitude')
    ax1.set_ylabel('Latitude')

    # 设置坐标轴刻度为0.05°
    set_axis_ticks(ax1, lon_bounds, lat_bounds)

    # 设置显示范围
    ax1.set_xlim(lon_bounds[0] - 0.02, lon_bounds[1] + 0.02)
    ax1.set_ylim(lat_bounds[0] - 0.02, lat_bounds[1] + 0.02)
    
    # 2. 流域边界详细图
    ax2 = axes[0, 1]
    watershed_gdf.plot(ax=ax2, facecolor='lightblue', edgecolor='red', linewidth=2, alpha=0.7)
    ax2.set_title('Watershed Boundary Detail', fontweight='bold')
    ax2.set_xlabel('Longitude')
    ax2.set_ylabel('Latitude')

    # 设置坐标轴刻度为0.05°
    set_axis_ticks(ax2, lon_bounds, lat_bounds)

    # 设置显示范围
    ax2.set_xlim(lon_bounds[0] - 0.02, lon_bounds[1] + 0.02)
    ax2.set_ylim(lat_bounds[0] - 0.02, lat_bounds[1] + 0.02)
    
    # 3. 裁剪后的栅格数据
    ax3 = axes[1, 0]
    clipped_data.plot(ax=ax3, cmap='Blues', add_colorbar=True)
    watershed_gdf.boundary.plot(ax=ax3, color='red', linewidth=2)
    ax3.set_title('Clipped Grid Data', fontweight='bold')
    ax3.set_xlabel('Longitude')
    ax3.set_ylabel('Latitude')

    # 设置坐标轴刻度为0.05°
    set_axis_ticks(ax3, lon_bounds, lat_bounds)

    # 设置显示范围
    ax3.set_xlim(lon_bounds[0] - 0.02, lon_bounds[1] + 0.02)
    ax3.set_ylim(lat_bounds[0] - 0.02, lat_bounds[1] + 0.02)

    # 4. 保留的栅格单元格
    ax4 = axes[1, 1]

    # 转换为DataFrame获取有效点
    clipped_df = clipped_ds.to_dataframe().reset_index().dropna()

    # 绘制栅格单元格
    if len(clipped_df) > 0:
        norm, colormap = draw_grid_cells(ax4,
                                       clipped_df['longitude'].values,
                                       clipped_df['latitude'].values,
                                       clipped_df[data_var].values,
                                       cmap='Blues',
                                       alpha=0.8,
                                       edgecolor='black',
                                       linewidth=1.0)

        # 添加颜色条
        sm = plt.cm.ScalarMappable(cmap=colormap, norm=norm)
        sm.set_array([])
        plt.colorbar(sm, ax=ax4, label=f'{data_var} value')

    # 绘制流域边界
    watershed_gdf.boundary.plot(ax=ax4, color='red', linewidth=2)

    ax4.set_title(f'Retained Grid Cells (Total: {len(clipped_df)})', fontweight='bold')
    ax4.set_xlabel('Longitude')
    ax4.set_ylabel('Latitude')

    # 设置坐标轴刻度为0.05°
    set_axis_ticks(ax4, lon_bounds, lat_bounds)

    # 设置显示范围
    ax4.set_xlim(lon_bounds[0] - 0.02, lon_bounds[1] + 0.02)
    ax4.set_ylim(lat_bounds[0] - 0.02, lat_bounds[1] + 0.02)

    # 调整布局
    plt.tight_layout()

    # 保存图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to: {save_path}")

    plt.show()

    # 打印统计信息
    print(f"\n=== {watershed_name.upper()} Clipping Statistics ===")
    print(f"Original grid shape: {original_data.shape}")
    print(f"Clipped grid shape: {clipped_data.shape}")
    print(f"Valid points after clipping: {len(clipped_df)}")
    print(f"Data range: {clipped_df[data_var].min():.4f} to {clipped_df[data_var].max():.4f}")
    print(f"Watershed area bounds:")
    print(f"  Longitude: {watershed_gdf.bounds.minx.iloc[0]:.4f} to {watershed_gdf.bounds.maxx.iloc[0]:.4f}")
    print(f"  Latitude: {watershed_gdf.bounds.miny.iloc[0]:.4f} to {watershed_gdf.bounds.maxy.iloc[0]:.4f}")

def create_comparison_plot(watersheds_data, save_path=None):
    """
    创建多个流域的对比图
    
    Parameters:
    -----------
    watersheds_data : dict
        包含多个流域数据的字典
    save_path : str, optional
        保存路径
    """
    n_watersheds = len(watersheds_data)
    fig, axes = plt.subplots(2, n_watersheds, figsize=(6*n_watersheds, 10))
    
    if n_watersheds == 1:
        axes = axes.reshape(2, 1)
    
    fig.suptitle('Watershed Clipping Comparison', fontsize=16, fontweight='bold')
    
    for i, (watershed_name, data) in enumerate(watersheds_data.items()):
        original_ds, watershed_gdf, clipped_ds = data
        data_var = list(original_ds.data_vars)[0]

        # 检查数据维度并选择合适的索引方式
        data_dims = original_ds[data_var].dims

        # 根据实际维度选择数据
        if 'time' in data_dims and 'step' in data_dims:
            original_data = original_ds[data_var].isel(time=0, step=0)
        elif 'step' in data_dims:
            original_data = original_ds[data_var].isel(step=0)
        else:
            original_data = original_ds[data_var]

        # 获取流域边界范围
        bounds = watershed_gdf.bounds.iloc[0]
        lon_bounds = (bounds['minx'], bounds['maxx'])
        lat_bounds = (bounds['miny'], bounds['maxy'])

        # 上排：原始数据 + 边界
        ax_top = axes[0, i]
        original_data.plot(ax=ax_top, cmap='Blues', add_colorbar=True)
        watershed_gdf.boundary.plot(ax=ax_top, color='red', linewidth=2)
        ax_top.set_title(f'{watershed_name.upper()}\nOriginal + Boundary', fontweight='bold')

        # 设置坐标轴刻度为0.05°
        set_axis_ticks(ax_top, lon_bounds, lat_bounds)
        ax_top.set_xlim(lon_bounds[0] - 0.02, lon_bounds[1] + 0.02)
        ax_top.set_ylim(lat_bounds[0] - 0.02, lat_bounds[1] + 0.02)

        # 下排：裁剪后数据（显示栅格单元）
        ax_bottom = axes[1, i]

        # 转换为DataFrame获取有效点
        clipped_df = clipped_ds.to_dataframe().reset_index().dropna()

        # 绘制栅格单元格
        if len(clipped_df) > 0:
            _, _ = draw_grid_cells(ax_bottom,
                                 clipped_df['longitude'].values,
                                 clipped_df['latitude'].values,
                                 clipped_df[data_var].values,
                                 cmap='Blues',
                                 alpha=0.8,
                                 edgecolor='black',
                                 linewidth=1.0)

        # 绘制流域边界
        watershed_gdf.boundary.plot(ax=ax_bottom, color='red', linewidth=2)
        ax_bottom.set_title(f'{watershed_name.upper()}\nClipped Grid Cells', fontweight='bold')

        # 设置坐标轴刻度为0.05°
        set_axis_ticks(ax_bottom, lon_bounds, lat_bounds)
        ax_bottom.set_xlim(lon_bounds[0] - 0.02, lon_bounds[1] + 0.02)
        ax_bottom.set_ylim(lat_bounds[0] - 0.02, lat_bounds[1] + 0.02)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Comparison plot saved to: {save_path}")
    
    plt.show()

def inspect_grib_structure(grib_file):
    """检查GRIB2文件结构"""
    print(f"Inspecting GRIB2 file structure: {grib_file}")

    ds = xr.open_dataset(grib_file, engine='cfgrib')
    print(f"Dataset dimensions: {ds.dims}")
    print(f"Dataset coordinates: {list(ds.coords)}")
    print(f"Dataset variables: {list(ds.data_vars)}")
    print(f"Dataset shape: {ds.dims}")

    # 打印每个变量的详细信息
    for var_name in ds.data_vars:
        var = ds[var_name]
        print(f"\nVariable '{var_name}':")
        print(f"  Dimensions: {var.dims}")
        print(f"  Shape: {var.shape}")
        print(f"  Data type: {var.dtype}")

    ds.close()
    return ds

def main():
    """主函数"""
    # 定义流域和shapefile路径
    watersheds = {
        'lianghe': '/home/<USER>/Flood_flow_prediction/boundary/lianghe.shp',
        'yantang': '/home/<USER>/Flood_flow_prediction/boundary/yantang.shp',
        'yinhe': '/home/<USER>/Flood_flow_prediction/boundary/yinhe.shp'
    }

    # 选择一个示例GRIB2文件进行可视化
    # 这里使用2025_final中的第一个可用文件
    source_dir = Path("/home/<USER>/Flood_flow_prediction/China_Product/2025_final")

    # 查找第一个包含GRB2文件的日期文件夹
    grib_file = None
    for date_folder in sorted(source_dir.iterdir()):
        if date_folder.is_dir():
            grb2_files = list(date_folder.glob("*.GRB2"))
            if grb2_files:
                grib_file = str(grb2_files[0])
                print(f"Using GRIB2 file: {grib_file}")
                break

    if not grib_file:
        print("Error: No GRIB2 files found in the source directory!")
        return

    # 首先检查GRIB2文件结构
    try:
        inspect_grib_structure(grib_file)

        # 为每个流域创建可视化
        watersheds_data = {}

        for watershed_name, shapefile_path in watersheds.items():
            print(f"\nProcessing {watershed_name} watershed...")

            try:
                # 加载数据
                original_ds, watershed_gdf, clipped_ds = load_and_prepare_data(grib_file, shapefile_path)
                watersheds_data[watershed_name] = (original_ds, watershed_gdf, clipped_ds)

                # 创建单独的可视化
                save_path = f"China_Product/{watershed_name}_clipping_visualization.png"
                create_visualization(original_ds, watershed_gdf, clipped_ds, watershed_name, save_path)

            except Exception as e:
                print(f"Error processing {watershed_name}: {e}")
                import traceback
                traceback.print_exc()
                continue

        # 创建对比图
        if watersheds_data:
            comparison_save_path = "China_Product/watersheds_clipping_comparison.png"
            create_comparison_plot(watersheds_data, comparison_save_path)

            print(f"\n=== Summary ===")
            print(f"Successfully processed {len(watersheds_data)} watersheds")
            print(f"Individual visualizations saved as: *_clipping_visualization.png")
            print(f"Comparison plot saved as: watersheds_clipping_comparison.png")
        else:
            print("No watersheds were successfully processed.")

    except Exception as e:
        print(f"Error in main function: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
